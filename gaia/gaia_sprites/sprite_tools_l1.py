
from .spr_agf_agsearch import quick_search
#from .spr_tamarind import market_research
#from .spr_ext_search import web_search, news_search
from .spr_agf_omnisearch import omni_search
from .spr_ext_wiki import wiki_search
from .spr_agf_multimodel import multimodel_query
from .spr_agf_frames import frame_search, frame_list
from .spr_agf_investors import investor_search, investor_stats
from .spr_llm_smart import (
    llm_completion_text, llm_completion_json, llm_analyze_code,
    llm_generate_code, llm_explain_concept, llm_debug_error
)
from .spr_ext_deep_research import (
    deep_research_simple, deep_research_robust, deep_research_batch,
    deep_research_load, deep_research_list
)
